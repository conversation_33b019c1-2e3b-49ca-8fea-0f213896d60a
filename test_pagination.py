#!/usr/bin/env python3
"""
Test pagination functionality for Expert Review Dashboard
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_pagination():
    """Test pagination for both events and samples."""
    
    print("🧪 Testing Pagination Functionality")
    print("=" * 60)
    
    # Test 1: Test events pagination
    print("📝 Test 1: Test events pagination...")
    try:
        # Get first page
        response = requests.get(f"{BASE_URL}/datagen/events?limit=5&offset=0")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Page 1: Found {data['total_count']} total events")
        print(f"   Returned {len(data['events'])} events on this page")
        
        if data['total_count'] > 5:
            # Get second page
            response2 = requests.get(f"{BASE_URL}/datagen/events?limit=5&offset=5")
            response2.raise_for_status()
            data2 = response2.json()
            
            print(f"✅ Page 2: Found {data2['total_count']} total events")
            print(f"   Returned {len(data2['events'])} events on this page")
            
            # Verify different events on different pages
            page1_ids = {event['id'] for event in data['events']}
            page2_ids = {event['id'] for event in data2['events']}
            
            if page1_ids.isdisjoint(page2_ids):
                print("✅ Pagination working correctly - different events on different pages")
            else:
                print("❌ Pagination issue - same events appearing on different pages")
        else:
            print("ℹ️ Not enough events to test pagination (need more than 5)")
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 2: Test samples pagination
    print("\n📝 Test 2: Test samples pagination...")
    try:
        # Get first page
        response = requests.get(f"{BASE_URL}/datasets/samples?limit=5&offset=0")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Page 1: Found {data['total_count']} total samples")
        print(f"   Returned {len(data['samples'])} samples on this page")
        
        if data['total_count'] > 5:
            # Get second page
            response2 = requests.get(f"{BASE_URL}/datasets/samples?limit=5&offset=5")
            response2.raise_for_status()
            data2 = response2.json()
            
            print(f"✅ Page 2: Found {data2['total_count']} total samples")
            print(f"   Returned {len(data2['samples'])} samples on this page")
            
            # Verify different samples on different pages
            page1_ids = {sample['id'] for sample in data['samples']}
            page2_ids = {sample['id'] for sample in data2['samples']}
            
            if page1_ids.isdisjoint(page2_ids):
                print("✅ Pagination working correctly - different samples on different pages")
            else:
                print("❌ Pagination issue - same samples appearing on different pages")
        else:
            print("ℹ️ Not enough samples to test pagination (need more than 5)")
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 3: Test expert filtering with pagination
    print("\n📝 Test 3: Test expert filtering with pagination...")
    try:
        response = requests.get(f"{BASE_URL}/datagen/events?expert_id=expert_001&limit=3&offset=0")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Expert filter: Found {data['total_count']} events for expert_001")
        print(f"   Returned {len(data['events'])} events on this page")
        
        if data['events']:
            for event in data['events']:
                experts = event.get('selected_experts', [])
                if 'expert_001' in experts:
                    print(f"   ✅ Event {event['id']} correctly assigned to expert_001")
                else:
                    print(f"   ❌ Event {event['id']} not assigned to expert_001 but returned in filter")
                    
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    # Test 4: Test event samples pagination
    print("\n📝 Test 4: Test event samples pagination...")
    try:
        # First get an event ID
        events_response = requests.get(f"{BASE_URL}/datagen/events?limit=1")
        events_response.raise_for_status()
        events_data = events_response.json()
        
        if events_data['events']:
            event_id = events_data['events'][0]['id']
            
            # Get samples for this event with pagination
            response = requests.get(f"{BASE_URL}/datasets/samples?datagen_event_id={event_id}&limit=3&offset=0")
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ Event samples: Found {data['total_count']} samples for event {event_id}")
            print(f"   Returned {len(data['samples'])} samples on this page")
            
            if data['total_count'] > 3:
                # Get second page
                response2 = requests.get(f"{BASE_URL}/datasets/samples?datagen_event_id={event_id}&limit=3&offset=3")
                response2.raise_for_status()
                data2 = response2.json()
                
                print(f"✅ Page 2: Found {data2['total_count']} total samples")
                print(f"   Returned {len(data2['samples'])} samples on this page")
        else:
            print("⚠️ No events found to test event samples pagination")
            
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False
    
    print("\n🎉 All pagination tests completed successfully!")
    return True

if __name__ == "__main__":
    test_pagination()
